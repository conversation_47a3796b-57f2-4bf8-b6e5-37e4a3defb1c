use regex::Regex;
use std::collections::HashMap;
use once_cell::sync::Lazy;

use crate::utils::{
    normalize_date,
    normalize_dns_question_name,
    normalize_time,
};

use crate::utils_classes::{
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    RouterConfig,
    RouterBoardConfig,
    SnortConfig,
    SquidConfig,
    SwitchConfig,
    UserAuditConfig,
    UserNoticeConfig,
    UserWarningConfig,
    VMwareConfig,
    VPNServerConfig,
    WindowsServerConfig,
    MYSQLValue,
};

use crate::utils_patterns::{
    DAEMON_PATTERN,
    FILTERLOG_PATTERN,
    SNORT_PATTERN,
    SQUID_PATTERN,
    USERAUDIT_PATTERN,
    USERNOTICE_PATTERN,
    USERWARNING_PATTERN,
    ROUTER_PATTERN,
    ROUTERBOARD_PATTERN,
    SWITCH_PATTERN,
    VMWARE_PATTERN,
    WINDOW<PERSON>ERVER_PATTERN,
    WS_AN_AD_PATTERN,
    WS_SW_PATTERN,
    DHCP_PATTERN,
    DNS_PATTERN,
    DNS_REST_PATTERN,
    VPNSERVER_PATTERN,
};

fn _is_invalid_ln(ln: &str) -> bool {
    if ln.is_empty() ||
       ln.contains("ERROR name exceeds safe print buffer length") ||
       ln.contains("ERROR length byte") ||
       ln.contains("leads outside message") ||
       ln.contains("Exiting on signal") ||
       ln.contains("Now monitoring attacks") ||
       ln.contains("spp_arpspoof") ||
       ln.contains("because it is a directory, not a file") ||
       !ln.chars().next().unwrap_or(' ').is_ascii_digit() {
        return true;
    }
    false
}

fn _invalid_line_sections(
    object_list_of_names_and_addresses: &[String],
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
    line_object: &str,
    line_event_type: &str,
    line_alert_type: &str,
) -> bool {
    if event_types.is_empty() && filterby.is_empty() && line_object.is_empty() {
        return false;
    }

    if !line_object.is_empty() && !object_list_of_names_and_addresses.contains(&line_object.to_string()) {
        return true;
    }

    if !event_types.is_empty() && !event_types.contains(&line_event_type.to_string()) {
        return true;
    }

    if filterby_is_in_alert_type && !line_alert_type.contains(filterby) {
        return true;
    }

    false
}

fn _extract_matches_from_pattern(string: &str, pattern: &Regex) -> Option<Vec<String>> {
    if let Some(captures) = pattern.captures(string) {
        let mut groups = Vec::new();
        for i in 1..captures.len() {
            groups.push(captures.get(i).map_or("".to_string(), |m| m.as_str().to_string()));
        }
        Some(groups)
    } else {
        None
    }
}

fn _parse_snort(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &SNORT_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,
        event_types,
        filterby_is_in_alert_type,
        filterby,
        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let final_sensor = if let Some(mapped_sensor) = object_dict_of_addresses_and_names.get(line_sensor) {
        mapped_sensor.clone()
    } else {
        line_sensor.clone()
    };

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        splited[5].clone(),
        splited[6].clone(),
        splited[7].clone(),
        splited[8].clone(),
        splited[9].clone(),
        splited[10].clone(),
        splited[11].clone(),
        splited[12].clone(),
        splited[13].clone(),
    ];

    (Some(final_sensor), Some(row))
}

fn _parse_daemon(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern(ln, &DAEMON_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let line_sensor = &splited[2];
    let line_event_type = &splited[3];
    let line_alert_type = &splited[4];

    if _invalid_line_sections(
        object_list_of_names_and_addresses,
        event_types,
        filterby_is_in_alert_type,
        filterby,
        line_sensor,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let final_sensor = if let Some(mapped_sensor) = object_dict_of_addresses_and_names.get(line_sensor) {
        mapped_sensor.clone()
    } else {
        line_sensor.clone()
    };

    let row = vec![
        splited[0].clone(),
        splited[1].clone(),
        line_event_type.clone(),
        line_alert_type.clone(),
        splited[5].clone(),
    ];

    (Some(final_sensor), Some(row))
}

static _PRECOMPUTED_VALUES: Lazy<HashMap<&'static str, (String, String, bool, Vec<String>)>> = Lazy::new(|| {
    let mut map = HashMap::new();

    let get_config_values = |config: &dyn Fn() -> (MYSQLValue, MYSQLValue, MYSQLValue, MYSQLValue)| -> (String, String, bool, Vec<String>) {
        let (slug, filterby, filterby_is_in_alert_type, event_types) = config();

        let slug_str = match slug {
            MYSQLValue::Str(s) => s,
            _ => String::new(),
        };

        let filterby_str = match filterby {
            MYSQLValue::Str(s) => s,
            _ => String::new(),
        };

        let filterby_bool = match filterby_is_in_alert_type {
            MYSQLValue::Bool(b) => b,
            _ => false,
        };

        let event_types_vec = match event_types {
            MYSQLValue::List(v) => v,
            _ => Vec::new(),
        };

        (slug_str, filterby_str, filterby_bool, event_types_vec)
    };

    map.insert("FilterLogConfig", get_config_values(&|| {
        (FilterLogConfig::SLUG.value(), FilterLogConfig::FILTERBY.value(), FilterLogConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), FilterLogConfig::EVENT_TYPES.value())
    }));

    map.insert("SnortConfig", get_config_values(&|| {
        (SnortConfig::SLUG.value(), SnortConfig::FILTERBY.value(), SnortConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), SnortConfig::EVENT_TYPES.value())
    }));

    map.insert("DaemonConfig", get_config_values(&|| {
        (DaemonConfig::SLUG.value(), DaemonConfig::FILTERBY.value(), DaemonConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), DaemonConfig::EVENT_TYPES.value())
    }));

    map.insert("VPNServerConfig", get_config_values(&|| {
        (VPNServerConfig::SLUG.value(), VPNServerConfig::FILTERBY.value(), VPNServerConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), VPNServerConfig::EVENT_TYPES.value())
    }));

    map.insert("WindowsServerConfig", get_config_values(&|| {
        (WindowsServerConfig::SLUG.value(), WindowsServerConfig::FILTERBY.value(), WindowsServerConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), WindowsServerConfig::EVENT_TYPES.value())
    }));

    map.insert("DNSConfig", get_config_values(&|| {
        (DNSConfig::SLUG.value(), DNSConfig::FILTERBY.value(), DNSConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), DNSConfig::EVENT_TYPES.value())
    }));

    map.insert("DHCPConfig", get_config_values(&|| {
        (DHCPConfig::SLUG.value(), DHCPConfig::FILTERBY.value(), DHCPConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), DHCPConfig::EVENT_TYPES.value())
    }));

    map.insert("UserWarningConfig", get_config_values(&|| {
        (UserWarningConfig::SLUG.value(), UserWarningConfig::FILTERBY.value(), UserWarningConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), UserWarningConfig::EVENT_TYPES.value())
    }));

    map.insert("SwitchConfig", get_config_values(&|| {
        (SwitchConfig::SLUG.value(), SwitchConfig::FILTERBY.value(), SwitchConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), SwitchConfig::EVENT_TYPES.value())
    }));

    map.insert("UserNoticeConfig", get_config_values(&|| {
        (UserNoticeConfig::SLUG.value(), UserNoticeConfig::FILTERBY.value(), UserNoticeConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), UserNoticeConfig::EVENT_TYPES.value())
    }));

    map.insert("UserAuditConfig", get_config_values(&|| {
        (UserAuditConfig::SLUG.value(), UserAuditConfig::FILTERBY.value(), UserAuditConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), UserAuditConfig::EVENT_TYPES.value())
    }));

    map.insert("SquidConfig", get_config_values(&|| {
        (SquidConfig::SLUG.value(), SquidConfig::FILTERBY.value(), SquidConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), SquidConfig::EVENT_TYPES.value())
    }));

    map.insert("RouterConfig", get_config_values(&|| {
        (RouterConfig::SLUG.value(), RouterConfig::FILTERBY.value(), RouterConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), RouterConfig::EVENT_TYPES.value())
    }));

    map.insert("RouterBoardConfig", get_config_values(&|| {
        (RouterBoardConfig::SLUG.value(), RouterBoardConfig::FILTERBY.value(), RouterBoardConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), RouterBoardConfig::EVENT_TYPES.value())
    }));

    map.insert("VMwareConfig", get_config_values(&|| {
        (VMwareConfig::SLUG.value(), VMwareConfig::FILTERBY.value(), VMwareConfig::FILTERBY_IS_IN_ALERT_TYPE.value(), VMwareConfig::EVENT_TYPES.value())
    }));

    map
});

static _PARSERS: Lazy<HashMap<String, fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>)>> = Lazy::new(|| {
    let mut map = HashMap::new();

    let snort_slug = match SnortConfig::SLUG.value() {
        MYSQLValue::Str(s) => s,
        _ => String::new(),
    };
    map.insert(snort_slug, _parse_snort as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));

    let daemon_slug = match DaemonConfig::SLUG.value() {
        MYSQLValue::Str(s) => s,
        _ => String::new(),
    };
    map.insert(daemon_slug, _parse_daemon as fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>));

    map
});

pub fn parse_ln(
    ln: &str,
    cls: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
) -> (Option<String>, Option<Vec<String>>) {
    if _is_invalid_ln(ln) {
        return (None, None);
    }

    let (slug, filterby, filterby_is_in_alert_type, event_types) = match _PRECOMPUTED_VALUES.get(cls) {
        Some(values) => values,
        None => return (None, None),
    };

    if !filterby.is_empty() && !filterby_is_in_alert_type && !ln.contains(filterby) {
        return (None, None);
    }

    let func = match _PARSERS.get(slug) {
        Some(f) => f,
        None => return (None, None),
    };

    func(
        ln,
        object_list_of_names_and_addresses,
        object_dict_of_addresses_and_names,
        event_types,
        *filterby_is_in_alert_type,
        filterby,
    )
}
